package com.ruiheng.xmuse.core.network

import com.blankj.utilcode.util.DeviceUtils
import com.blankj.utilcode.util.EncryptUtils
import com.blankj.utilcode.util.PhoneUtils
import com.blankj.utilcode.util.SPUtils
import com.ruiheng.xmuse.core.common.result.encodeToBase64String
import com.ruiheng.xmuse.core.network.course.CourseService
import com.ruiheng.xmuse.core.network.model.user.RequestUserResult
import com.ruiheng.xmuse.core.network.other.ConfigService
import com.ruiheng.xmuse.core.network.other.OtherService
import com.ruiheng.xmuse.core.network.other.WxService
import com.ruiheng.xmuse.core.network.sleep.SleepRxService
import com.ruiheng.xmuse.core.network.sleep.SleepService
import com.ruiheng.xmuse.core.network.user.FileService
import com.ruiheng.xmuse.core.network.user.UserService
import com.ruiheng.xmuse.core.network.util.LiveDataCallAdapterFactory
import com.ruiheng.xmuse.core.network.util.gson.BaseGsonConverterFactory
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.FormBody
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.adapter.rxjava3.RxJava3CallAdapterFactory
import retrofit2.converter.gson.GsonConverterFactory
import java.util.Locale
import javax.inject.Singleton
import kotlin.random.Random

@Module
@InstallIn(SingletonComponent::class)
object RetrofitNetworkModule {

    @Provides
    @Singleton
    fun provideRetrofitWxService(resultInterceptor: RequestResultInterceptor): WxService {
        return provideWxService(resultInterceptor)
    }

    @Provides
    @Singleton
    fun provideRetrofitUserService(resultInterceptor: RequestResultInterceptor): UserService {
        return provideRetrofitClient(resultInterceptor).create(UserService::class.java)
    }

    @Provides
    @Singleton
    fun provideRetrofitSettingService(resultInterceptor: RequestResultInterceptor? = null): OtherService {
        return provideRetrofitClient(resultInterceptor).create(OtherService::class.java)
    }

    @Provides
    @Singleton
    fun provideRetrofitFileService(): FileService {
        return provideRetrofitFileClient().create(FileService::class.java)
    }

    @Provides
    @Singleton
    fun provideRetrofitCourseService(resultInterceptor: RequestResultInterceptor? = null): CourseService {
        return provideRetrofitClient(resultInterceptor).create(CourseService::class.java)
    }

    @Provides
    @Singleton
    fun provideRetrofitSleepService(resultInterceptor: RequestResultInterceptor? = null): SleepService {
        return provideRetrofitClient(resultInterceptor).create(SleepService::class.java)
    }

    @Provides
    @Singleton
    fun provideRxRetrofitSleepService(resultInterceptor: RequestResultInterceptor? = null): SleepRxService {
        return provideRxRetrofitClient(resultInterceptor).create(SleepRxService::class.java)
    }

    @Provides
    @Singleton
    fun provideRetrofitConfigService(resultInterceptor: RequestResultInterceptor? = null): ConfigService {
        return provideConfigRetrofitClient(resultInterceptor).create(ConfigService::class.java)
    }

    fun startApp(isLocalTest: Boolean, isTest: Boolean) {
        SPUtils.getInstance().put("isTest", isTest)
        SPUtils.getInstance().put("isLocalTest", isLocalTest)
        saveUserToken("")
    }

    var aliyunApmStarted: Boolean = false
    val aliyunAppKey = "335514886"
    val aliyunAppScret = "030ee72dd7ed4f2db20d6ebb59f63c11"
    val aliyunAppRsaScret =
        "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCzhL5BIBUAaH77GZc5Wpwg0RjNFwL7R5SmnAGDZk7VHW9eCa2EVzx3zbBWFVx8kaOBVoR0C1CSK40DQi2c08tlhUL4737XqnCiDopq6RsFBxwb50Qlgx0bvDGhnf7Ra1OJZV3hxs/SDONPgmPfqmPDYZVLXzR8B7AcunfvejeprQIDAQAB"
    var IS_LOCAL_TEST = SPUtils.getInstance().getBoolean("isLocalTest", false)
    var IS_TEST = SPUtils.getInstance().getBoolean("isTest", true)

    init {
        IS_LOCAL_TEST = SPUtils.getInstance().getBoolean("isLocalTest", false)
        IS_TEST = SPUtils.getInstance().getBoolean("isTest", true)
    }

    private const val HOST_LOCAL_TEST = "http://***********:9051"

    private const val HOST_TEST = "https://test-cloud.xmuse.cn/cloud/"
    private const val HOST_PUBLISH = "https://api.xmuse.cn/"

    val USER_PROFILE_URL: String =
        if (IS_TEST) "https://test-cloud.xmuse.cn" else "https://qn.xmuse.cn"

    const val userProtocol =
        "https://oss.xmuse.cn/eeg-data/policy%26protocol/Xmuse_User_Protocol.html"
    const val privacyPolicy =
        "https://oss.xmuse.cn/eeg-data/policy%26protocol/Xmuse_Privacy_Policy.html"

    private val HOST: String =
        if (IS_LOCAL_TEST) HOST_LOCAL_TEST else if (IS_TEST) HOST_TEST else HOST_PUBLISH

    fun getSleepReportPage(reportId:String,userToken:String) = " https://test-cloud.xmuse.cn/sleepreport/?id=${reportId}&t=${userToken}"

    private fun provideWxService(resultInterceptor: RequestResultInterceptor? = null):WxService{
        val logging = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
        val okHttpClient = OkHttpClient.Builder().apply {
            if (resultInterceptor != null) {
                addInterceptor(resultInterceptor)
            }
            addInterceptor(logging)
        }.build()

        return Retrofit.Builder()
            .baseUrl("https://api.weixin.qq.com/")
            .client(okHttpClient)
            .addConverterFactory(BaseGsonConverterFactory.create())
            .addCallAdapterFactory(LiveDataCallAdapterFactory())
            .build()
            .create(WxService::class.java)
    }

    private fun provideRetrofitClient(resultInterceptor: RequestResultInterceptor? = null): Retrofit {
        val logging = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
        val okHttpClient = OkHttpClient.Builder().apply {
            if (resultInterceptor != null) {
                addInterceptor(resultInterceptor)
            }
            addInterceptor { provideUserTokenHeader(it) }
            addInterceptor(logging)
        }.build()

        return Retrofit.Builder()
            .baseUrl(HOST)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    private fun provideRxRetrofitClient(resultInterceptor: RequestResultInterceptor? = null): Retrofit {
        val logging = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
        val okHttpClient = OkHttpClient.Builder().apply {
            if (resultInterceptor != null) {
                addInterceptor(resultInterceptor)
            }
            addInterceptor { provideUserTokenHeader(it) }
            addInterceptor(logging)
        }.build()

        return Retrofit.Builder()
            .baseUrl(HOST)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .addCallAdapterFactory(RxJava3CallAdapterFactory.create())
            .build()
    }

    private fun provideRetrofitFileClient(): Retrofit {
        val logging = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
        val okHttpClient = OkHttpClient.Builder()
            .addInterceptor(ContentTypeInterceptor())
            .addInterceptor { provideUserTokenHeader(it) }
            .addInterceptor(logging)
            .build()

        return Retrofit.Builder()
            .baseUrl(HOST)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    /**
     * 提供配置服务的Retrofit客户端
     * 使用不同的baseUrl: https://api-fort.itingluo.com/
     */
    private fun provideConfigRetrofitClient(resultInterceptor: RequestResultInterceptor? = null): Retrofit {
        val logging = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
        val okHttpClient = OkHttpClient.Builder().apply {
            if (resultInterceptor != null) {
                addInterceptor(resultInterceptor)
            }
            // 配置服务可能不需要用户token，根据实际需求决定是否添加
            // addInterceptor { provideUserTokenHeader(it) }
            addInterceptor(logging)
        }.build()

        return Retrofit.Builder()
            .baseUrl("https://api-fort.itingluo.com/")
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    fun saveUserToken(userResult: RequestUserResult) {
        SPUtils.getInstance().put("userToken", userResult.token)
    }

    fun saveUserToken(token: String) {
        SPUtils.getInstance().put("userToken", token)
    }

    fun provideUserToken(): String? {
        return SPUtils.getInstance().getString("userToken", null)
    }

    private fun provideUserTokenHeader(chain: Interceptor.Chain): Response {
        val original = chain.request()
        val path = original.url.encodedPath
        val key = "0104"
        val version = "1"
        val sign = generateRequestSign(key, version, path)
        var requestBuilder = original.newBuilder()
        requestBuilder = requestBuilder.addHeader("s", sign)
        requestBuilder = requestBuilder.addHeader("f", key)
        requestBuilder = requestBuilder.addHeader("v", version)

        val token = SPUtils.getInstance().getString("userToken", "") //账号token
        if (!token.isNullOrEmpty()) {
            requestBuilder = requestBuilder.addHeader("t", token)
        }
        val request = requestBuilder.build()
        val param = StringBuilder()
        try {
            val body = request.body
            if (body is FormBody) {
                for (i in 0 until body.size) {
                    val name = body.encodedName(i)
                    val value = body.encodedValue(i)
                    param.append("$name:$value")
                }
            }
        } catch (e: Exception) {
        }
        return chain.proceed(request)
    }

    class ContentTypeInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val originalRequest: Request = chain.request()
            val newRequest: Request = originalRequest.newBuilder()
                .header("Content-Type", "multipart/form-data") // 修改Content-Type
                .build()
            return chain.proceed(newRequest)
        }
    }

    private fun generateRequestSign(key: String, version: String, url: String): String {
        val f = "2024v${version}@muse@${key}"
        val timeMillis = System.currentTimeMillis()
        val randomString = generateRandomString(4)
        val md5String = EncryptUtils.encryptMD5ToString("${randomString}${timeMillis}${url}${f}")
            .lowercase(Locale.ROOT)
        val sign =
            "${randomString}&${timeMillis}&${md5String}"
        return sign.encodeToBase64String()
    }

    private fun generateRandomString(length: Int): String {
        if (length < 4) {
            throw IllegalArgumentException("Length must be at least 4")
        }
        val charPool = ('0'..'9') + ('A'..'Z') + ('a'..'z')
        val random = Random.Default
        val sb = StringBuilder(length)

        repeat(length) {
            sb.append(charPool[random.nextInt(charPool.size)])
        }

        return sb.toString()
    }

}
