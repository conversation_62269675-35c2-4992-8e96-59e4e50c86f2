package com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback

import com.interaxon.muse.session.data_tracking.neurofeedback.TflitePresenterModel

enum class BusymindData {
    ALPHA, BETA, DELTA, GAMMA, THETA, MIND, BODY, HEART, BREATH, FOX_SCORE, HBO_LEFT, HBO_RIGHT, HBR_LEFT, HBR_RIGHT,
    PPG_HRV_HF, PPG_HRV_LF, PPG_HRV_LFvsHF, PPG_HRV_PNN50, PPG_HRV_RMSSD, PPG_HRV_SDNN;

    fun getPresenterModel(): TflitePresenterModel {
        return when (this) {
            ALPHA, BETA, DELTA, GAMMA, THETA -> TflitePresenterModel.BAND_POWER_GRAPH

            BREATH, BODY, MIND -> TflitePresenterModel.TEN_HZ_INPUT_TIMESERIES

            HEART, PPG_HRV_HF, PPG_HRV_LF, PPG_HRV_LFvsHF, PPG_HRV_PNN50, PPG_HRV_RMSSD, PPG_HRV_SDNN -> TflitePresenterModel.SIXTY_FOUR_HZ_INPUT_TIMESERIES
            FOX_SCORE -> TflitePresenterModel.FOX_SCORE_GRAPH
            HBO_LEFT, HBO_RIGHT, HBR_LEFT, HBR_RIGHT -> TflitePresenterModel.CONCENTRATIONS_SCORE_GRAPH
        }
    }

}