//#include <jni.h>
//#include <string>
//#include <vector>
//#include <iostream>
////专注力指标
//// 检查一个数是否为 NaN
//bool isNaN(double value) {
//    return std::isnan(value);
//}
////bufferA：对应的是Android_SDK的α的相对值;bufferB：对应的是Android_SDK的β的相对值;bufferC：对应的是Android_SDK的γ的相对值;bufferD：对应的是Android_SDK的θ的相对值;bufferE：对应的是Android_SDK的δ的相对值
//// 计算注意力值的函数
//double attentionValue(const std::vector<double>& bufferA, const std::vector<double>& bufferB, const std::vector<double>& bufferC,
//                      const std::vector<double>& bufferD, const std::vector<double>& bufferE, const std::vector<double>& hsi, int electrode_num) {
//    double sumA = 0, sumB = 0, sumD = 0;
//    int validCountA = 0, validCountB = 0, validCountD = 0;
//
//    for (int i = 0; i < electrode_num; ++i) {
//        if (hsi[i] <= 2) {
//            if (!isNaN(bufferA[i])) {
//                sumA += bufferA[i];
//                validCountA++;
//            }
//            if (!isNaN(bufferB[i])) {
//                sumB += bufferB[i];
//                validCountB++;
//            }
//            if (!isNaN(bufferD[i])) {
//                sumD += bufferD[i];
//                validCountD++;
//            }
//        }
//    }
//    if (validCountA > 0 && validCountD > 0 && (sumA / validCountA + sumD / validCountD) != 0) {
//        double total = (sumB / validCountB) / (sumA / validCountA + sumD / validCountD);
//        return (total / (1 + total)) * 100;
//    }
//    return 0;
//}
////冥想度
//double relaxValue(const std::vector<double>& bufferA, const std::vector<double>& bufferB, const std::vector<double>& bufferC, const std::vector<double>& bufferD,const std::vector<double>& bufferE,const std::vector<double>& hsi, int electrode_num) {
//    double sumA = 0, sumB = 0;
//    int validCountA = 0, validCountB = 0;
//    for (int i = 0; i < electrode_num; ++i) {
//        if (hsi[i] <= 2) {
//            if (!isNaN(bufferA[i])) {
//                sumA += bufferA[i];
//                validCountA++;
//            }
//            if (!isNaN(bufferB[i])) {
//                sumB += bufferB[i];
//                validCountB++;
//            }
//        }
//    }
//
//    if (validCountA > 0 && validCountB > 0 && (sumA / validCountA + sumB / validCountB) != 0) {
//        double total = (sumA / validCountA) / (sumB / validCountB );
//        return (total / (1 + total)) * 100;
//    }
//    return 0;
//}
////压力
//double stressValue(const std::vector<double>& bufferA, const std::vector<double>& bufferB, const std::vector<double>& bufferC,const std::vector<double>& bufferD,const std::vector<double>& bufferE, const std::vector<double>& hsi, int electrode_num){
//    double sumA = 0, sumB = 0, sumC = 0;
//    int validCountA = 0, validCountB = 0, validCountC = 0;
//    for (int i = 0; i < electrode_num; ++i) {
//        if (hsi[i] <= 2) {
//            if (!isNaN(bufferA[i])) {
//                sumA += bufferA[i];
//                validCountA++;
//            }
//            if (!isNaN(bufferB[i])) {
//                sumB += bufferB[i];
//                validCountB++;
//            }
//            if (!isNaN(bufferC[i])) {
//                sumC += bufferC[i];
//                validCountC++;
//            }
//        }
//    }
//    if (validCountA > 0 && validCountB > 0 && ((sumB / validCountB + sumC / validCountC ) / (sumA / validCountA )) != 0) {
//        double total = (sumB / validCountB + sumC / validCountC ) / (sumA / validCountA );
//        return (total / (1 + total)) * 100;
//    }
//    return 0;
//}
////焦虑
//double anxietyValue(const std::vector<double>& bufferA, const std::vector<double>& bufferB, const std::vector<double>& bufferC, const std::vector<double>& bufferD,const std::vector<double>& bufferE,const std::vector<double>& hsi, int electrode_num){
//    double sumB = 0, sumE = 0;
//    int validCountB = 0, validCountE = 0;
//    for (int i = 0; i < electrode_num; ++i) {
//        if (hsi[i] <= 2) {
//            if (!isNaN(bufferB[i])) {
//                sumB += bufferB[i];
//                validCountB++;
//            }
//            if (!isNaN(bufferE[i])) {
//                sumE += bufferE[i];
//                validCountE++;
//            }
//        }
//    }
//    if (validCountB > 0 && validCountE > 0 && (sumB / validCountB + sumE / validCountE) != 0) {
//        double total = (sumB / validCountB) / (sumE / validCountE );
//        return (total / (1 + total)) * 100;
//    }
//    return 0;
//}
////注意广度
//double attention_width_Value(const std::vector<double>& bufferA, const std::vector<double>& bufferB, const std::vector<double>& bufferC, const std::vector<double>& bufferD, const std::vector<double>& bufferE, const std::vector<double>& hsi, int electrode_num) {
//    double sumA = 0, sumC = 0;
//    int validCountA = 0, validCountC = 0;
//    for (int i = 0; i < electrode_num; ++i) {
//        if (hsi[i] <= 2) {
//            if (!isNaN(bufferA[i])) {
//                sumA += bufferA[i];
//                validCountA++;
//            }
//            if (!isNaN(bufferC[i])) {
//                sumC += bufferC[i];
//                validCountC++;
//            }
//        }
//    }
//    if (validCountA > 0 && validCountC > 0 && (sumA / validCountA + sumC / validCountC) != 0) {
//        double total = (sumA / validCountA) / (sumC / validCountC);
//        return (total / (1 + total)) * 100;
//    }
//    return 0;
//}
//
////注意稳定性
//double attention_stability_Value(const std::vector<double>& bufferA, const std::vector<double>& bufferB, const std::vector<double>& bufferC, const std::vector<double>& bufferD, const std::vector<double>& bufferE, const std::vector<double>& hsi, int electrode_num) {
//    double sumA = 0
//    int validCountA = 0
//    for (int i = 0; i < electrode_num; ++i) {
//        if (hsi[i] <= 2) {
//            if (!isNaN(bufferA[i])) {
//                sumA += bufferA[i];
//                validCountA++;
//            }
//        }
//    }
//    if (validCountA > 0 && (sumA / validCountA ) != 0) {
//        double total = (sumA / validCountA) ;
//        return total  * 100;
//    }
//    return 0;
//}
////注意转移
//double attention_transfer_Value(const std::vector<double>& bufferA, const std::vector<double>& bufferB, const std::vector<double>& bufferC, const std::vector<double>& bufferD, const std::vector<double>& bufferE, const std::vector<double>& hsi, int electrode_num) {
//    double sum A = 0, sumB = 0;
//    int validCountA = 0, validCountB = 0;
//    for (int i = 0; i < electrode_num; ++i) {
//        if (hsi[i] <= 2) {
//            if (!isNaN(bufferA[i])) {
//                sumA += bufferA[i];
//                validCountA++;
//            }
//            if (!isNaN(bufferB[i])) {
//                sumB += bufferB[i];
//                validCountB++;
//            }
//        }
//    }
//    if (validCountB > 0 && validCountA > 0 && (sumA / validCountA ) != 0 && (sumB / validCountB) != 0) {
//        double total = ((sumB / validCountB)+(sumA / validCountA))/2;
//        return total * 100;
//    }
//    return 0;
//}
////注意分配
//double attention_share_Value(const std::vector<double>& bufferA, const std::vector<double>& bufferB, const std::vector<double>& bufferC, const std::vector<double>& bufferD, const std::vector<double>& bufferE, const std::vector<double>& hsi, int electrode_num) {
//    double sumB = 0, sumC = 0;
//    int validCountB = 0, validCountC = 0;
//    for (int i = 0; i < electrode_num; ++i) {
//        if (hsi[i] <= 2) {
//            if (!isNaN(bufferB[i])) {
//                sumB += bufferB[i];
//                validCountB++;
//            }
//            if (!isNaN(bufferC[i])) {
//                sumC += bufferC[i];
//                validCountC++;
//            }
//        }
//    }
//    if (validCountB > 0 && validCountC > 0 && (sumB / validCountB + sumC / validCountC) != 0) {
//        double total = (sumC / validCountC) / (sumB / validCountB);
//        return (total / (1 + total)) * 100;
//    }
//    return 0;
//}
////工作记忆
//double work_memory_Value(const std::vector<double>& bufferA, const std::vector<double>& bufferB, const std::vector<double>& bufferC, const std::vector<double>& bufferD, const std::vector<double>& bufferE, const std::vector<double>& hsi, int electrode_num) {
//    double sumB = 0, sumD = 0;
//    int validCountB = 0, validCountD = 0;
//    for (int i = 0; i < electrode_num; ++i) {
//        if (hsi[i] <= 2) {
//            if (!isNaN(bufferB[i])) {
//                sumB += bufferB[i];
//                validCountB++;
//            }
//            if (!isNaN(bufferD[i])) {
//                sumD += bufferD[i];
//                validCountD++;
//            }
//        }
//    }
//    if (validCountB > 0 && validCountD > 0 && (sumB / validCountB) != 0 && (sumD / validCountD) != 0) {
//        double total = (sumB / validCountB)*(sumD / validCountD);
//		double total2 = std::sqrt(total);
//        return total2  * 100;
//    }
//    return 0;
//}
////情绪认知
//double emotional_cognition_Value(const std::vector<double>& bufferA, const std::vector<double>& bufferB, const std::vector<double>& bufferC, const std::vector<double>& bufferD, const std::vector<double>& bufferE, const std::vector<double>& hsi, int electrode_num) {
//    double sumA = 0, sumD = 0;
//    int validCountA = 0, validCountD = 0;
//    for (int i = 0; i < electrode_num; ++i) {
//        if (hsi[i] <= 2) {
//            if (!isNaN(bufferA[i])) {
//                sumA += bufferA[i];
//                validCountA++;
//            }
//            if (!isNaN(bufferD[i])) {
//                sumD += bufferD[i];
//                validCountD++;
//            }
//        }
//    }
//    if (validCountA > 0 && validCountD > 0 && (sumA / validCountA ) != 0) {
//        double total = ((sumA / validCountA) + (sumD / validCountD))/2;
//        return total  * 100;
//    }
//    return 0;
//}
////注意执行
//double attention_execute_Value(const std::vector<double>& bufferA, const std::vector<double>& bufferB, const std::vector<double>& bufferC, const std::vector<double>& bufferD, const std::vector<double>& bufferE, const std::vector<double>& hsi, int electrode_num) {
//    double sumB = 0, sumC = 0;
//    int validCountB = 0, validCountC = 0;
//    for (int i = 0; i < electrode_num; ++i) {
//        if (hsi[i] <= 2) {
//            if (!isNaN(bufferB[i])) {
//                sumB += bufferB[i];
//                validCountB++;
//            }
//            if (!isNaN(bufferC[i])) {
//                sumC += bufferC[i];
//                validCountC++;
//            }
//        }
//    }
//    if (validCountB > 0 && validCountC > 0 && (sumB / validCountB) != 0) {
//        double total = (sumB / validCountB)*(sumC / validCountC);
//		double total2 = std::sqrt(total);
//        return  total2  * 100;
//    }
//    return 0;
//}
//
//
//
//
//extern "C" JNIEXPORT jdoubleArray JNICALL
//Java_com_beiyang_XmuseEEGCalculation_XmuseEEGindex_attentionMethod(JNIEnv *env, jobject /* this */, jdoubleArray javaArray1, jdoubleArray javaArray2, jdoubleArray javaArray3, jdoubleArray javaArray4,jdoubleArray javaArray5,jdoubleArray hsiArray, jint electrode_num)
//{
//    // 将Java数组转换为C++向量
//    jdouble* array1 = env->GetDoubleArrayElements(javaArray1, 0);
//    jdouble* array2 = env->GetDoubleArrayElements(javaArray2, 0);
//    jdouble* array3 = env->GetDoubleArrayElements(javaArray3, 0);
//    jdouble* array4 = env->GetDoubleArrayElements(javaArray4, 0);
//    jdouble* array5 = env->GetDoubleArrayElements(javaArray5, 0);
//    jdouble* hsi = env->GetDoubleArrayElements(hsiArray, 0);
//    //α，β，θ和hsi的值
//    std::vector<double> bufferA(array1, array1 + env->GetArrayLength(javaArray1));
//    std::vector<double> bufferB(array2, array2 + env->GetArrayLength(javaArray2));
//    std::vector<double> bufferC(array3, array3 + env->GetArrayLength(javaArray3));
//    std::vector<double> bufferD(array4, array4 + env->GetArrayLength(javaArray4));
//    std::vector<double> bufferE(array5, array5 + env->GetArrayLength(javaArray5));
//    std::vector<double> hsiVector(hsi, hsi + env->GetArrayLength(hsiArray));
//
//    // 计算注意力
//    double attention = attentionValue(bufferA, bufferB, bufferC, bufferD,bufferE,hsiVector, electrode_num);
//
//    // 释放JNI数组元素
//    env->ReleaseDoubleArrayElements(javaArray1, array1, 0);
//    env->ReleaseDoubleArrayElements(javaArray2, array2, 0);
//    env->ReleaseDoubleArrayElements(javaArray3, array3, 0);
//    env->ReleaseDoubleArrayElements(javaArray4, array4, 0);
//    env->ReleaseDoubleArrayElements(javaArray5, array5, 0);
//    env->ReleaseDoubleArrayElements(hsiArray, hsi, 0);
//
//    // 创建一个包含注意力的数组
//    jdouble outputArray[1];
//    outputArray[0] = attention;
//    // 创建一个新的jdoubleArray来存储结果
//    jdoubleArray resultArray = env->NewDoubleArray(1);
//    // 将计算结果复制到新数组
//    env->SetDoubleArrayRegion(resultArray, 0, 1, outputArray);
//    // 返回包含注意力的数组
//    return resultArray;
//}
//
//
//extern "C" JNIEXPORT jdoubleArray JNICALL
//Java_com_beiyang_XmuseEEGCalculation_XmuseEEGindex_relaxMethod(JNIEnv *env, jobject /* this */, jdoubleArray javaArray1, jdoubleArray javaArray2, jdoubleArray javaArray3,  jdoubleArray javaArray4,jdoubleArray javaArray5,jdoubleArray hsiArray, jint electrode_num)
//{
//    // 将Java数组转换为C++向量
//    jdouble* array1 = env->GetDoubleArrayElements(javaArray1, 0);
//    jdouble* array2 = env->GetDoubleArrayElements(javaArray2, 0);
//    jdouble* array3 = env->GetDoubleArrayElements(javaArray3, 0);
//    jdouble* array4 = env->GetDoubleArrayElements(javaArray4, 0);
//    jdouble* array5 = env->GetDoubleArrayElements(javaArray5, 0);
//    jdouble* hsi = env->GetDoubleArrayElements(hsiArray, 0);
//    //α，β，θ和hsi的值
//    std::vector<double> bufferA(array1, array1 + env->GetArrayLength(javaArray1));
//    std::vector<double> bufferB(array2, array2 + env->GetArrayLength(javaArray2));
//    std::vector<double> bufferC(array3, array3 + env->GetArrayLength(javaArray3));
//    std::vector<double> bufferD(array4, array4 + env->GetArrayLength(javaArray4));
//    std::vector<double> bufferE(array5, array5 + env->GetArrayLength(javaArray5));
//    std::vector<double> hsiVector(hsi, hsi + env->GetArrayLength(hsiArray));
//
//    // 计算冥想度
//    double relaxation = relaxValue(bufferA, bufferB, bufferC, bufferD,bufferE,hsiVector, electrode_num);
//
//    // 释放JNI数组元素
//    env->ReleaseDoubleArrayElements(javaArray1, array1, 0);
//    env->ReleaseDoubleArrayElements(javaArray2, array2, 0);
//    env->ReleaseDoubleArrayElements(javaArray3, array3, 0);
//    env->ReleaseDoubleArrayElements(javaArray4, array4, 0);
//    env->ReleaseDoubleArrayElements(javaArray5, array5, 0);
//    env->ReleaseDoubleArrayElements(hsiArray, hsi, 0);
//
//    // 创建一个放松度的数组
//    jdouble outputArray[1];
//    outputArray[0] = relaxation;
//
//    // 创建一个新的jdoubleArray来存储结果
//    jdoubleArray resultArray = env->NewDoubleArray(1);
//    // 将计算结果复制到新数组
//    env->SetDoubleArrayRegion(resultArray, 0, 1, outputArray);
//
//    // 返回包含放松度的数组
//    return resultArray;
//}
//
//
//extern "C" JNIEXPORT jdoubleArray JNICALL
//Java_com_beiyang_XmuseEEGCalculation_XmuseEEGindex_stressMethod(JNIEnv *env, jobject /* this */, jdoubleArray javaArray1, jdoubleArray javaArray2, jdoubleArray javaArray3, jdoubleArray javaArray4,jdoubleArray javaArray5, jdoubleArray hsiArray, jint electrode_num)
//{
//    // 将Java数组转换为C++向量
//    jdouble* array1 = env->GetDoubleArrayElements(javaArray1, 0);
//    jdouble* array2 = env->GetDoubleArrayElements(javaArray2, 0);
//    jdouble* array3 = env->GetDoubleArrayElements(javaArray3, 0);
//    jdouble* array4 = env->GetDoubleArrayElements(javaArray4, 0);
//    jdouble* array5 = env->GetDoubleArrayElements(javaArray5, 0);
//    jdouble* hsi = env->GetDoubleArrayElements(hsiArray, 0);
//    //α，β，θ和hsi的值
//    std::vector<double> bufferA(array1, array1 + env->GetArrayLength(javaArray1));
//    std::vector<double> bufferB(array2, array2 + env->GetArrayLength(javaArray2));
//    std::vector<double> bufferC(array3, array3 + env->GetArrayLength(javaArray3));
//    std::vector<double> bufferD(array4, array4 + env->GetArrayLength(javaArray4));
//    std::vector<double> bufferE(array5, array5 + env->GetArrayLength(javaArray5));
//    std::vector<double> hsiVector(hsi, hsi + env->GetArrayLength(hsiArray));
//
//    // 计算压力
//    double stressation = stressValue(bufferA, bufferB, bufferC, bufferD,bufferE,hsiVector, electrode_num);
//
//    // 释放JNI数组元素
//    env->ReleaseDoubleArrayElements(javaArray1, array1, 0);
//    env->ReleaseDoubleArrayElements(javaArray2, array2, 0);
//    env->ReleaseDoubleArrayElements(javaArray3, array3, 0);
//    env->ReleaseDoubleArrayElements(javaArray4, array4, 0);
//    env->ReleaseDoubleArrayElements(javaArray5, array5, 0);
//    env->ReleaseDoubleArrayElements(hsiArray, hsi, 0);
//
//    // 创建一个放松度的数组
//    jdouble outputArray[1];
//    outputArray[0] = stressation;
//
//    // 创建一个新的jdoubleArray来存储结果
//    jdoubleArray resultArray = env->NewDoubleArray(1);
//    // 将计算结果复制到新数组
//    env->SetDoubleArrayRegion(resultArray, 0, 1, outputArray);
//
//    // 返回包含放松度的数组
//    return resultArray;
//}
//
//
//
//extern "C" JNIEXPORT jdoubleArray JNICALL
//Java_com_beiyang_XmuseEEGCalculation_XmuseEEGindex_anxietyMethod(JNIEnv *env, jobject /* this */, jdoubleArray javaArray1, jdoubleArray javaArray2, jdoubleArray javaArray3,  jdoubleArray javaArray4,jdoubleArray javaArray5,jdoubleArray hsiArray, jint electrode_num)
//{
//    // 将Java数组转换为C++向量
//    jdouble* array1 = env->GetDoubleArrayElements(javaArray1, 0);
//    jdouble* array2 = env->GetDoubleArrayElements(javaArray2, 0);
//    jdouble* array3 = env->GetDoubleArrayElements(javaArray3, 0);
//    jdouble* array4 = env->GetDoubleArrayElements(javaArray4, 0);
//    jdouble* array5 = env->GetDoubleArrayElements(javaArray5, 0);
//    jdouble* hsi = env->GetDoubleArrayElements(hsiArray, 0);
//    //α，β，θ和hsi的值
//    std::vector<double> bufferA(array1, array1 + env->GetArrayLength(javaArray1));
//    std::vector<double> bufferB(array2, array2 + env->GetArrayLength(javaArray2));
//    std::vector<double> bufferC(array3, array3 + env->GetArrayLength(javaArray3));
//    std::vector<double> bufferD(array4, array4 + env->GetArrayLength(javaArray4));
//    std::vector<double> bufferE(array5, array5 + env->GetArrayLength(javaArray5));
//    std::vector<double> hsiVector(hsi, hsi + env->GetArrayLength(hsiArray));
//
//    // 计算
//    double anxietyation = anxietyValue(bufferA, bufferB, bufferC, bufferD,bufferE,hsiVector, electrode_num);
//
//    // 释放JNI数组元素
//    env->ReleaseDoubleArrayElements(javaArray1, array1, 0);
//    env->ReleaseDoubleArrayElements(javaArray2, array2, 0);
//    env->ReleaseDoubleArrayElements(javaArray3, array3, 0);
//    env->ReleaseDoubleArrayElements(javaArray4, array4, 0);
//    env->ReleaseDoubleArrayElements(javaArray5, array5, 0);
//    env->ReleaseDoubleArrayElements(hsiArray, hsi, 0);
//
//    // 创建一个放松度的数组
//    jdouble outputArray[1];
//    outputArray[0] = anxietyation;
//
//    // 创建一个新的jdoubleArray来存储结果
//    jdoubleArray resultArray = env->NewDoubleArray(1);
//    // 将计算结果复制到新数组
//    env->SetDoubleArrayRegion(resultArray, 0, 1, outputArray);
//
//    // 返回包含放松度的数组
//    return resultArray;
//}
//
//
//
//extern "C" JNIEXPORT jdoubleArray JNICALL
//Java_com_beiyang_XmuseEEGCalculation_XmuseEEGindex_widthMethod(JNIEnv* env, jobject /* this */, jdoubleArray javaArray1, jdoubleArray javaArray2, jdoubleArray javaArray3, jdoubleArray javaArray4, jdoubleArray javaArray5, jdoubleArray hsiArray, jint electrode_num)
//{
//    // 将Java数组转换为C++向量
//    jdouble* array1 = env->GetDoubleArrayElements(javaArray1, 0);
//    jdouble* array2 = env->GetDoubleArrayElements(javaArray2, 0);
//    jdouble* array3 = env->GetDoubleArrayElements(javaArray3, 0);
//    jdouble* array4 = env->GetDoubleArrayElements(javaArray4, 0);
//    jdouble* array5 = env->GetDoubleArrayElements(javaArray5, 0);
//    jdouble* hsi = env->GetDoubleArrayElements(hsiArray, 0);
//    //α，β，θ和hsi的值
//    std::vector<double> bufferA(array1, array1 + env->GetArrayLength(javaArray1));
//    std::vector<double> bufferB(array2, array2 + env->GetArrayLength(javaArray2));
//    std::vector<double> bufferC(array3, array3 + env->GetArrayLength(javaArray3));
//    std::vector<double> bufferD(array4, array4 + env->GetArrayLength(javaArray4));
//    std::vector<double> bufferE(array5, array5 + env->GetArrayLength(javaArray5));
//    std::vector<double> hsiVector(hsi, hsi + env->GetArrayLength(hsiArray));
//
//    // 计算
//    double anxietyation = attention_width_Value(bufferA, bufferB, bufferC, bufferD, bufferE, hsiVector, electrode_num);
//
//    // 释放JNI数组元素
//    env->ReleaseDoubleArrayElements(javaArray1, array1, 0);
//    env->ReleaseDoubleArrayElements(javaArray2, array2, 0);
//    env->ReleaseDoubleArrayElements(javaArray3, array3, 0);
//    env->ReleaseDoubleArrayElements(javaArray4, array4, 0);
//    env->ReleaseDoubleArrayElements(javaArray5, array5, 0);
//    env->ReleaseDoubleArrayElements(hsiArray, hsi, 0);
//
//    // 创建一个数组
//    jdouble outputArray[1];
//    outputArray[0] = anxietyation;
//
//    // 创建一个新的jdoubleArray来存储结果
//    jdoubleArray resultArray = env->NewDoubleArray(1);
//    // 将计算结果复制到新数组
//    env->SetDoubleArrayRegion(resultArray, 0, 1, outputArray);
//
//    // 返回包含数组
//    return resultArray;
//}
//
//
//
//
//
//
//extern "C" JNIEXPORT jdoubleArray JNICALL
//Java_com_beiyang_XmuseEEGCalculation_XmuseEEGindex_stabilityMethod(JNIEnv* env, jobject /* this */, jdoubleArray javaArray1, jdoubleArray javaArray2, jdoubleArray javaArray3, jdoubleArray javaArray4, jdoubleArray javaArray5, jdoubleArray hsiArray, jint electrode_num)
//{
//    // 将Java数组转换为C++向量
//    jdouble* array1 = env->GetDoubleArrayElements(javaArray1, 0);
//    jdouble* array2 = env->GetDoubleArrayElements(javaArray2, 0);
//    jdouble* array3 = env->GetDoubleArrayElements(javaArray3, 0);
//    jdouble* array4 = env->GetDoubleArrayElements(javaArray4, 0);
//    jdouble* array5 = env->GetDoubleArrayElements(javaArray5, 0);
//    jdouble* hsi = env->GetDoubleArrayElements(hsiArray, 0);
//    //α，β，θ和hsi的值
//    std::vector<double> bufferA(array1, array1 + env->GetArrayLength(javaArray1));
//    std::vector<double> bufferB(array2, array2 + env->GetArrayLength(javaArray2));
//    std::vector<double> bufferC(array3, array3 + env->GetArrayLength(javaArray3));
//    std::vector<double> bufferD(array4, array4 + env->GetArrayLength(javaArray4));
//    std::vector<double> bufferE(array5, array5 + env->GetArrayLength(javaArray5));
//    std::vector<double> hsiVector(hsi, hsi + env->GetArrayLength(hsiArray));
//
//    // 计算
//    double anxietyation = attention_stability_Value(bufferA, bufferB, bufferC, bufferD, bufferE, hsiVector, electrode_num);
//
//    // 释放JNI数组元素
//    env->ReleaseDoubleArrayElements(javaArray1, array1, 0);
//    env->ReleaseDoubleArrayElements(javaArray2, array2, 0);
//    env->ReleaseDoubleArrayElements(javaArray3, array3, 0);
//    env->ReleaseDoubleArrayElements(javaArray4, array4, 0);
//    env->ReleaseDoubleArrayElements(javaArray5, array5, 0);
//    env->ReleaseDoubleArrayElements(hsiArray, hsi, 0);
//
//    // 创建一个数组
//    jdouble outputArray[1];
//    outputArray[0] = anxietyation;
//
//    // 创建一个新的jdoubleArray来存储结果
//    jdoubleArray resultArray = env->NewDoubleArray(1);
//    // 将计算结果复制到新数组
//    env->SetDoubleArrayRegion(resultArray, 0, 1, outputArray);
//
//    // 返回包含数组
//    return resultArray;
//}
//
//
//
//extern "C" JNIEXPORT jdoubleArray JNICALL
//Java_com_beiyang_XmuseEEGCalculation_XmuseEEGindex_transferMethod(JNIEnv* env, jobject /* this */, jdoubleArray javaArray1, jdoubleArray javaArray2, jdoubleArray javaArray3, jdoubleArray javaArray4, jdoubleArray javaArray5, jdoubleArray hsiArray, jint electrode_num)
//{
//    // 将Java数组转换为C++向量
//    jdouble* array1 = env->GetDoubleArrayElements(javaArray1, 0);
//    jdouble* array2 = env->GetDoubleArrayElements(javaArray2, 0);
//    jdouble* array3 = env->GetDoubleArrayElements(javaArray3, 0);
//    jdouble* array4 = env->GetDoubleArrayElements(javaArray4, 0);
//    jdouble* array5 = env->GetDoubleArrayElements(javaArray5, 0);
//    jdouble* hsi = env->GetDoubleArrayElements(hsiArray, 0);
//    //α，β，θ和hsi的值
//    std::vector<double> bufferA(array1, array1 + env->GetArrayLength(javaArray1));
//    std::vector<double> bufferB(array2, array2 + env->GetArrayLength(javaArray2));
//    std::vector<double> bufferC(array3, array3 + env->GetArrayLength(javaArray3));
//    std::vector<double> bufferD(array4, array4 + env->GetArrayLength(javaArray4));
//    std::vector<double> bufferE(array5, array5 + env->GetArrayLength(javaArray5));
//    std::vector<double> hsiVector(hsi, hsi + env->GetArrayLength(hsiArray));
//
//    // 计算
//    double anxietyation = attention_transfer_Value(bufferA, bufferB, bufferC, bufferD, bufferE, hsiVector, electrode_num);
//
//    // 释放JNI数组元素
//    env->ReleaseDoubleArrayElements(javaArray1, array1, 0);
//    env->ReleaseDoubleArrayElements(javaArray2, array2, 0);
//    env->ReleaseDoubleArrayElements(javaArray3, array3, 0);
//    env->ReleaseDoubleArrayElements(javaArray4, array4, 0);
//    env->ReleaseDoubleArrayElements(javaArray5, array5, 0);
//    env->ReleaseDoubleArrayElements(hsiArray, hsi, 0);
//
//    // 创建一个数组
//    jdouble outputArray[1];
//    outputArray[0] = anxietyation;
//
//    // 创建一个新的jdoubleArray来存储结果
//    jdoubleArray resultArray = env->NewDoubleArray(1);
//    // 将计算结果复制到新数组
//    env->SetDoubleArrayRegion(resultArray, 0, 1, outputArray);
//
//    // 返回包含数组
//    return resultArray;
//}
//
//
//
//
//extern "C" JNIEXPORT jdoubleArray JNICALL
//Java_com_beiyang_XmuseEEGCalculation_XmuseEEGindex_shareMethod(JNIEnv* env, jobject /* this */, jdoubleArray javaArray1, jdoubleArray javaArray2, jdoubleArray javaArray3, jdoubleArray javaArray4, jdoubleArray javaArray5, jdoubleArray hsiArray, jint electrode_num)
//{
//    // 将Java数组转换为C++向量
//    jdouble* array1 = env->GetDoubleArrayElements(javaArray1, 0);
//    jdouble* array2 = env->GetDoubleArrayElements(javaArray2, 0);
//    jdouble* array3 = env->GetDoubleArrayElements(javaArray3, 0);
//    jdouble* array4 = env->GetDoubleArrayElements(javaArray4, 0);
//    jdouble* array5 = env->GetDoubleArrayElements(javaArray5, 0);
//    jdouble* hsi = env->GetDoubleArrayElements(hsiArray, 0);
//    //α，β，θ和hsi的值
//    std::vector<double> bufferA(array1, array1 + env->GetArrayLength(javaArray1));
//    std::vector<double> bufferB(array2, array2 + env->GetArrayLength(javaArray2));
//    std::vector<double> bufferC(array3, array3 + env->GetArrayLength(javaArray3));
//    std::vector<double> bufferD(array4, array4 + env->GetArrayLength(javaArray4));
//    std::vector<double> bufferE(array5, array5 + env->GetArrayLength(javaArray5));
//    std::vector<double> hsiVector(hsi, hsi + env->GetArrayLength(hsiArray));
//
//    // 计算
//    double anxietyation = attention_share_Value(bufferA, bufferB, bufferC, bufferD, bufferE, hsiVector, electrode_num);
//
//    // 释放JNI数组元素
//    env->ReleaseDoubleArrayElements(javaArray1, array1, 0);
//    env->ReleaseDoubleArrayElements(javaArray2, array2, 0);
//    env->ReleaseDoubleArrayElements(javaArray3, array3, 0);
//    env->ReleaseDoubleArrayElements(javaArray4, array4, 0);
//    env->ReleaseDoubleArrayElements(javaArray5, array5, 0);
//    env->ReleaseDoubleArrayElements(hsiArray, hsi, 0);
//
//    // 创建一个数组
//    jdouble outputArray[1];
//    outputArray[0] = anxietyation;
//
//    // 创建一个新的jdoubleArray来存储结果
//    jdoubleArray resultArray = env->NewDoubleArray(1);
//    // 将计算结果复制到新数组
//    env->SetDoubleArrayRegion(resultArray, 0, 1, outputArray);
//
//    // 返回包含数组
//    return resultArray;
//}
//
//
//
//
//
//extern "C" JNIEXPORT jdoubleArray JNICALL
//Java_com_beiyang_XmuseEEGCalculation_XmuseEEGindex_memoryMethod(JNIEnv* env, jobject /* this */, jdoubleArray javaArray1, jdoubleArray javaArray2, jdoubleArray javaArray3, jdoubleArray javaArray4, jdoubleArray javaArray5, jdoubleArray hsiArray, jint electrode_num)
//{
//    // 将Java数组转换为C++向量
//    jdouble* array1 = env->GetDoubleArrayElements(javaArray1, 0);
//    jdouble* array2 = env->GetDoubleArrayElements(javaArray2, 0);
//    jdouble* array3 = env->GetDoubleArrayElements(javaArray3, 0);
//    jdouble* array4 = env->GetDoubleArrayElements(javaArray4, 0);
//    jdouble* array5 = env->GetDoubleArrayElements(javaArray5, 0);
//    jdouble* hsi = env->GetDoubleArrayElements(hsiArray, 0);
//    //α，β，θ和hsi的值
//    std::vector<double> bufferA(array1, array1 + env->GetArrayLength(javaArray1));
//    std::vector<double> bufferB(array2, array2 + env->GetArrayLength(javaArray2));
//    std::vector<double> bufferC(array3, array3 + env->GetArrayLength(javaArray3));
//    std::vector<double> bufferD(array4, array4 + env->GetArrayLength(javaArray4));
//    std::vector<double> bufferE(array5, array5 + env->GetArrayLength(javaArray5));
//    std::vector<double> hsiVector(hsi, hsi + env->GetArrayLength(hsiArray));
//
//    // 计算
//    double anxietyation = work_memory_Value(bufferA, bufferB, bufferC, bufferD, bufferE, hsiVector, electrode_num);
//
//    // 释放JNI数组元素
//    env->ReleaseDoubleArrayElements(javaArray1, array1, 0);
//    env->ReleaseDoubleArrayElements(javaArray2, array2, 0);
//    env->ReleaseDoubleArrayElements(javaArray3, array3, 0);
//    env->ReleaseDoubleArrayElements(javaArray4, array4, 0);
//    env->ReleaseDoubleArrayElements(javaArray5, array5, 0);
//    env->ReleaseDoubleArrayElements(hsiArray, hsi, 0);
//
//    // 创建一个数组
//    jdouble outputArray[1];
//    outputArray[0] = anxietyation;
//
//    // 创建一个新的jdoubleArray来存储结果
//    jdoubleArray resultArray = env->NewDoubleArray(1);
//    // 将计算结果复制到新数组
//    env->SetDoubleArrayRegion(resultArray, 0, 1, outputArray);
//
//    // 返回包含数组
//    return resultArray;
//}
//
//
//
//
//
//
//
//extern "C" JNIEXPORT jdoubleArray JNICALL
//Java_com_beiyang_XmuseEEGCalculation_XmuseEEGindex_cognitionMethod(JNIEnv* env, jobject /* this */, jdoubleArray javaArray1, jdoubleArray javaArray2, jdoubleArray javaArray3, jdoubleArray javaArray4, jdoubleArray javaArray5, jdoubleArray hsiArray, jint electrode_num)
//{
//    // 将Java数组转换为C++向量
//    jdouble* array1 = env->GetDoubleArrayElements(javaArray1, 0);
//    jdouble* array2 = env->GetDoubleArrayElements(javaArray2, 0);
//    jdouble* array3 = env->GetDoubleArrayElements(javaArray3, 0);
//    jdouble* array4 = env->GetDoubleArrayElements(javaArray4, 0);
//    jdouble* array5 = env->GetDoubleArrayElements(javaArray5, 0);
//    jdouble* hsi = env->GetDoubleArrayElements(hsiArray, 0);
//    //α，β，θ和hsi的值
//    std::vector<double> bufferA(array1, array1 + env->GetArrayLength(javaArray1));
//    std::vector<double> bufferB(array2, array2 + env->GetArrayLength(javaArray2));
//    std::vector<double> bufferC(array3, array3 + env->GetArrayLength(javaArray3));
//    std::vector<double> bufferD(array4, array4 + env->GetArrayLength(javaArray4));
//    std::vector<double> bufferE(array5, array5 + env->GetArrayLength(javaArray5));
//    std::vector<double> hsiVector(hsi, hsi + env->GetArrayLength(hsiArray));
//
//    // 计算
//    double anxietyation = emotional_cognition_Value(bufferA, bufferB, bufferC, bufferD, bufferE, hsiVector, electrode_num);
//
//    // 释放JNI数组元素
//    env->ReleaseDoubleArrayElements(javaArray1, array1, 0);
//    env->ReleaseDoubleArrayElements(javaArray2, array2, 0);
//    env->ReleaseDoubleArrayElements(javaArray3, array3, 0);
//    env->ReleaseDoubleArrayElements(javaArray4, array4, 0);
//    env->ReleaseDoubleArrayElements(javaArray5, array5, 0);
//    env->ReleaseDoubleArrayElements(hsiArray, hsi, 0);
//
//    // 创建一个数组
//    jdouble outputArray[1];
//    outputArray[0] = anxietyation;
//
//    // 创建一个新的jdoubleArray来存储结果
//    jdoubleArray resultArray = env->NewDoubleArray(1);
//    // 将计算结果复制到新数组
//    env->SetDoubleArrayRegion(resultArray, 0, 1, outputArray);
//
//    // 返回包含数组
//    return resultArray;
//}
//
//
//
//
////注意执行
//
//extern "C" JNIEXPORT jdoubleArray JNICALL
//Java_com_beiyang_XmuseEEGCalculation_XmuseEEGindex_executeMethod(JNIEnv* env, jobject /* this */, jdoubleArray javaArray1, jdoubleArray javaArray2, jdoubleArray javaArray3, jdoubleArray javaArray4, jdoubleArray javaArray5, jdoubleArray hsiArray, jint electrode_num)
//{
//    // 将Java数组转换为C++向量
//    jdouble* array1 = env->GetDoubleArrayElements(javaArray1, 0);
//    jdouble* array2 = env->GetDoubleArrayElements(javaArray2, 0);
//    jdouble* array3 = env->GetDoubleArrayElements(javaArray3, 0);
//    jdouble* array4 = env->GetDoubleArrayElements(javaArray4, 0);
//    jdouble* array5 = env->GetDoubleArrayElements(javaArray5, 0);
//    jdouble* hsi = env->GetDoubleArrayElements(hsiArray, 0);
//    //α，β，θ和hsi的值
//    std::vector<double> bufferA(array1, array1 + env->GetArrayLength(javaArray1));
//    std::vector<double> bufferB(array2, array2 + env->GetArrayLength(javaArray2));
//    std::vector<double> bufferC(array3, array3 + env->GetArrayLength(javaArray3));
//    std::vector<double> bufferD(array4, array4 + env->GetArrayLength(javaArray4));
//    std::vector<double> bufferE(array5, array5 + env->GetArrayLength(javaArray5));
//    std::vector<double> hsiVector(hsi, hsi + env->GetArrayLength(hsiArray));
//
//    // 计算
//    double anxietyation = attention_execute_Value(bufferA, bufferB, bufferC, bufferD, bufferE, hsiVector, electrode_num);
//
//    // 释放JNI数组元素
//    env->ReleaseDoubleArrayElements(javaArray1, array1, 0);
//    env->ReleaseDoubleArrayElements(javaArray2, array2, 0);
//    env->ReleaseDoubleArrayElements(javaArray3, array3, 0);
//    env->ReleaseDoubleArrayElements(javaArray4, array4, 0);
//    env->ReleaseDoubleArrayElements(javaArray5, array5, 0);
//    env->ReleaseDoubleArrayElements(hsiArray, hsi, 0);
//
//    // 创建一个数组
//    jdouble outputArray[1];
//    outputArray[0] = anxietyation;
//
//    // 创建一个新的jdoubleArray来存储结果
//    jdoubleArray resultArray = env->NewDoubleArray(1);
//    // 将计算结果复制到新数组
//    env->SetDoubleArrayRegion(resultArray, 0, 1, outputArray);
//
//    // 返回包含的数组
//    return resultArray;
//}