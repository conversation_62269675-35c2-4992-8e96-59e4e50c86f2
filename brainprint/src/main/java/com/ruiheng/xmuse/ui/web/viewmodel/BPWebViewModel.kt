package com.ruiheng.xmuse.ui.web

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.session.data_tracking.neurofeedback.BusymindData
import com.ruiheng.xmuse.core.network.RetrofitNetworkModule
import com.ruiheng.xmuse.core.ui.ui.web.CommandUserToken
import com.ruiheng.xmuse.ui.web.createBPBusymindReportCommand
import com.ruiheng.xmuse.ui.web.handler.WebCommandHandler
import com.ruiheng.xmuse.ui.web.repository.WebCacheRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * BPWebActivity的ViewModel
 * 负责管理Web页面的数据和业务逻辑
 */
@HiltViewModel
class BPWebViewModel @Inject constructor(
    private val webCommandHandler: WebCommandHandler,
    private val webCacheRepository: WebCacheRepository
) : ViewModel() {

    // Web页面URL
    private val _webUrl = MutableLiveData<String>()
    val webUrl: LiveData<String> = _webUrl

    // 用户Token
    private val _userToken = MutableLiveData<String>()
    val userToken: LiveData<String> = _userToken

    // 命令字符串（用于特殊数据传递）
    private val _commandString = MutableLiveData<String>()
    val commandString: LiveData<String> = _commandString

    // 页面标题
    private val _pageTitle = MutableLiveData<String>()
    val pageTitle: LiveData<String> = _pageTitle

    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    // 错误信息
    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    // JavaScript调用结果
    private val _jsCallResult = MutableLiveData<String>()
    val jsCallResult: LiveData<String> = _jsCallResult

    /**
     * 初始化数据
     */
    fun initData(webUrl: String?, userToken: String?, commandString: String?) {
        _webUrl.value = webUrl ?: "https://www.xmuse.cn/"
        _userToken.value = userToken ?: ""
        _commandString.value = commandString ?: ""
        
        Timber.d("初始化Web数据: url=${_webUrl.value}, token=${_userToken.value}")
    }

    /**
     * 设置页面标题
     */
    fun setPageTitle(title: String?) {
        if (title?.startsWith("http") == false && title?.startsWith("qn.xmuse.cn") == false) {
            _pageTitle.value = title
        } else {
            _pageTitle.value = ""
        }
    }

    /**
     * 获取用户Token命令JSON
     */
    fun getUserTokenCommand(): String {
        // 总是获取最新的token，而不是使用初始化时传入的token
        val currentToken = RetrofitNetworkModule.provideBPUserToken() ?: ""
        return CommandUserToken(token = currentToken).toJson()
    }

    /**
     * 处理来自Web的命令
     */
    fun handleWebCommand(jsonString: String): String? {
        return try {
            Timber.d("收到Web消息: $jsonString")
            // 总是获取最新的token，而不是使用初始化时传入的token
            val currentToken = RetrofitNetworkModule.provideBPUserToken()
            webCommandHandler.executeCommand(jsonString, currentToken)
        } catch (e: Exception) {
            Timber.e(e, "处理Web消息失败")
            val errorResponse = mapOf(
                "command" to "error",
                "success" to false,
                "message" to "处理失败: ${e.message}"
            )
            Gson().toJson(errorResponse)
        }
    }

    /**
     * 创建BusymindReport命令
     */
    fun createBusymindReportCommand(reportData: Map<BusymindData, MutableList<Double>>): String {
        return createBPBusymindReportCommand(reportData)
    }

    /**
     * 发送响应到Web端
     */
    fun sendResponseToWeb(response: Map<String, Any?>) {
        viewModelScope.launch {
            val jsonString = Gson().toJson(response)
            _jsCallResult.value = "window.fromNative($jsonString)"
            Timber.d("发送响应到Web: $jsonString")
        }
    }

    /**
     * 发送错误信息到Web端
     */
    fun sendErrorToWeb(errorMessage: String) {
        val response = mapOf(
            "command" to "error",
            "success" to false,
            "message" to errorMessage
        )
        sendResponseToWeb(response)
    }

    /**
     * 设置加载状态
     */
    fun setLoading(loading: Boolean) {
        _isLoading.value = loading
    }

    /**
     * 设置错误信息
     */
    fun setError(error: String) {
        _errorMessage.value = error
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * 获取实际要加载的URL
     * 这里可以根据业务需求进行URL处理
     */
    fun getActualWebUrl(): String {
        // 目前硬编码为测试URL，后续可以根据配置或参数动态设置
//        return "http://192.168.10.8:8083/xmuse/home"
        // 正式环境应该使用: return _webUrl.value ?: "https://www.xmuse.cn/"
        return _webUrl.value ?: "https://www.xmuse.cn/"
    }

    /**
     * 页面开始加载
     */
    fun onPageStarted() {
        setLoading(true)
        clearError()
    }

    /**
     * 页面加载完成
     */
    fun onPageFinished() {
        setLoading(false)
    }

    /**
     * 页面加载错误
     */
    fun onPageError(error: String) {
        setLoading(false)
        setError(error)
    }

    override fun onCleared() {
        super.onCleared()
        Timber.d("BPWebViewModel cleared")
    }
}
