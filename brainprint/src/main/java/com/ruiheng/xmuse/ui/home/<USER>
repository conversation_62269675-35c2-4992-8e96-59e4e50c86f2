package com.ruiheng.xmuse.ui.home

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.Utils
import com.ruiheng.xmuse.core.common.result.Result
import com.ruiheng.xmuse.core.data.repository.SettingRepository
import com.ruiheng.xmuse.core.data.repository.UserDataRepository
import com.ruiheng.xmuse.core.data.repository.muse.interaxon.muse.MuseConnector
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesCourseRepository
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesSettingRepository
import com.ruiheng.xmuse.core.data.repository.net.NetResourcesUserRepository
import com.ruiheng.xmuse.core.network.model.other.KvConfigItem
import com.ruiheng.xmuse.core.data.util.GreetingTimeOfDay
import com.ruiheng.xmuse.core.data.util.TimeMonitor
import com.ruiheng.xmuse.core.model.data.UserData
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject
import com.ruiheng.xmuse.core.data.BP_TL_CLIENT_ID

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val userDataRepository: UserDataRepository,
    private val museConnector: MuseConnector,
    private val netResourcesUserRepository: NetResourcesUserRepository,
    private val settingRepository: SettingRepository
) : ViewModel() {

    private val _userDataStateFlow = MutableStateFlow<Result<UserData>>(Result.Loading())
    val userDataStateFlow: StateFlow<Result<UserData>> = _userDataStateFlow

    private val _xmuseDomainConfigStateFlow = MutableStateFlow<Result<KvConfigItem>>(Result.Loading())
    val xmuseDomainConfigStateFlow: StateFlow<Result<KvConfigItem>> = _xmuseDomainConfigStateFlow

    init {
        viewModelScope.launch {
            userDataRepository.userData.collect { user ->
                if (user != null) {
                    Log.v("zcy",user.token+"");
                    Log.v("zcy",user.bpAioToken.toString() + "");
                    _userDataStateFlow.value = Result.Success(user)


                } else {
                    _userDataStateFlow.value = Result.Error(Exception())
                    museConnector.disconnectMuse()
                }
            }
        }
    }

    fun quitUser() = netResourcesUserRepository.userLogout().stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(),
        initialValue = Result.Loading()
    )

    /**
     * 获取Xmuse域名配置
     */
    fun getXmuseDomainConfig() {
        Log.d("HomeViewModel", "开始获取域名配置...")
        viewModelScope.launch {
            settingRepository.getXmuseDomainConfig().collect { result ->
                Log.d("HomeViewModel", "配置获取结果: $result")
                _xmuseDomainConfigStateFlow.value = result
            }
        }
    }

    /**
     * 确保配置已加载的私有方法
     */
    private suspend fun ensureConfigLoaded() {
        if (_xmuseDomainConfigStateFlow.value !is Result.Success) {
            Log.d("HomeViewModel", "配置未成功加载，重新获取...")
            try {
                settingRepository.getXmuseDomainConfig().collect { result ->
                    Log.d("HomeViewModel", "重新获取配置结果: $result")
                    _xmuseDomainConfigStateFlow.value = result
                    return@collect
                }
            } catch (e: Exception) {
                Log.e("HomeViewModel", "获取配置失败", e)
            }
        }
    }

    /**
     * 获取配置URL的通用方法
     */
    private suspend fun getConfigUrl(
        urlType: String,
        urlExtractor: (KvConfigItem) -> String,
        defaultUrl: String
    ): String {
        Log.d("HomeViewModel", "开始获取${urlType}URL...")
        Log.d("HomeViewModel", "当前配置状态: ${_xmuseDomainConfigStateFlow.value}")

        // 确保配置已加载
        ensureConfigLoaded()

        // 获取基础URL（配置中的或默认的）
        val baseUrl = when (val configResult = _xmuseDomainConfigStateFlow.value) {
            is Result.Success -> {
                val url = urlExtractor(configResult.data)
                Log.d("HomeViewModel", "使用配置${urlType}URL: $url")
                url
            }
            else -> {
                Log.d("HomeViewModel", "使用默认${urlType}URL: $defaultUrl")
                defaultUrl
            }
        }

        // 拼接必要的参数
        val finalUrl = if (baseUrl.contains("?")) {
            // 如果URL已经包含参数，使用&连接
            "$baseUrl&tl_client_id=$BP_TL_CLIENT_ID&mac_addr=${com.ruiheng.xmuse.core.data.util.DeviceUtils.getUniqueId(Utils.getApp())}"
        } else {
            // 如果URL不包含参数，使用?开始参数
            "$baseUrl?tl_client_id=$BP_TL_CLIENT_ID&mac_addr=${com.ruiheng.xmuse.core.data.util.DeviceUtils.getUniqueId(Utils.getApp())}"
        }

        Log.d("HomeViewModel", "最终返回${urlType}URL: $finalUrl")
        return finalUrl
    }

    /**
     * 获取心理健康评估的URL，优先使用配置接口返回的home字段，失败时使用默认URL
     */
    suspend fun getMentalHealthAssessmentUrl(): String {
        return getConfigUrl(
            urlType = "心理健康评估",
            urlExtractor = { it.home },
            defaultUrl = com.ruiheng.xmuse.core.network.RetrofitNetworkModule.USER_PROFILE_URL+"?tl_client_id="+BP_TL_CLIENT_ID+"&mac_addr="+com.ruiheng.xmuse.core.data.util.DeviceUtils.getUniqueId(Utils.getApp())
        )
    }

    /**
     * 获取报告页面的URL，优先使用配置接口返回的report字段，失败时使用默认URL
     */
    suspend fun getReportUrl(): String {
        return getConfigUrl(
            urlType = "报告",
            urlExtractor = { it.report },
            defaultUrl = com.ruiheng.xmuse.core.network.RetrofitNetworkModule.USER_PROFILE_URL+"?tl_client_id="+BP_TL_CLIENT_ID+"&mac_addr="+com.ruiheng.xmuse.core.data.util.DeviceUtils.getUniqueId(Utils.getApp())
        )
    }

}




